<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== تشخيص مشاكل المهام ===\n\n";

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "1. فحص بنية جدول المهام:\n";
    $stmt = $pdo->query("DESCRIBE tasks");
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $col) {
        echo "- {$col['Field']}: {$col['Type']} " . 
             ($col['Null'] === 'YES' ? '(NULL)' : '(NOT NULL)') . 
             ($col['Default'] ? " DEFAULT '{$col['Default']}'" : '') . "\n";
    }
    
    echo "\n2. فحص المهام الموجودة:\n";
    $stmt = $pdo->query("SELECT * FROM tasks ORDER BY id DESC LIMIT 5");
    $tasks = $stmt->fetchAll();
    
    echo "عدد المهام الإجمالي: ";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tasks");
    $count = $stmt->fetch();
    echo $count['count'] . "\n\n";
    
    if (count($tasks) > 0) {
        echo "آخر 5 مهام:\n";
        foreach ($tasks as $task) {
            echo "ID: {$task['id']} | العنوان: {$task['title']} | ";
            echo "المستخدم: {$task['user_id']} | ";
            echo "الحالة: {$task['status']} | ";
            echo "تاريخ الإنشاء: {$task['created_at']}\n";
        }
    } else {
        echo "لا توجد مهام\n";
    }
    
    echo "\n3. اختبار تحديث حالة مهمة:\n";
    if (count($tasks) > 0) {
        $testTask = $tasks[0];
        $taskId = $testTask['id'];
        $currentStatus = $testTask['status'];
        $newStatus = ($currentStatus === 'completed') ? 'pending' : 'completed';

        echo "اختبار تحديث المهمة ID: $taskId\n";
        echo "الحالة الحالية: $currentStatus\n";
        echo "الحالة الجديدة: $newStatus\n";

        $stmt = $pdo->prepare("UPDATE tasks SET status = ? WHERE id = ?");
        $result = $stmt->execute([$newStatus, $taskId]);

        if ($result) {
            echo "✅ تم تحديث الحالة بنجاح\n";

            // التحقق من التحديث
            $stmt = $pdo->prepare("SELECT status FROM tasks WHERE id = ?");
            $stmt->execute([$taskId]);
            $updated = $stmt->fetch();

            echo "الحالة بعد التحديث: {$updated['status']}\n";
        } else {
            echo "❌ فشل في تحديث الحالة\n";
        }

        // إعادة الحالة الأصلية
        $stmt = $pdo->prepare("UPDATE tasks SET status = ? WHERE id = ?");
        $stmt->execute([$currentStatus, $taskId]);
    }
    
    echo "\n4. اختبار تحديث محتوى مهمة:\n";
    if (count($tasks) > 0) {
        $testTask = $tasks[0];
        $taskId = $testTask['id'];
        $originalTitle = $testTask['title'];
        $newTitle = $originalTitle . " (محدث)";
        
        echo "اختبار تحديث عنوان المهمة ID: $taskId\n";
        echo "العنوان الأصلي: $originalTitle\n";
        echo "العنوان الجديد: $newTitle\n";
        
        $stmt = $pdo->prepare("UPDATE tasks SET title = ? WHERE id = ?");
        $result = $stmt->execute([$newTitle, $taskId]);
        
        if ($result) {
            echo "✅ تم تحديث العنوان بنجاح\n";
            
            // التحقق من التحديث
            $stmt = $pdo->prepare("SELECT title FROM tasks WHERE id = ?");
            $stmt->execute([$taskId]);
            $updated = $stmt->fetch();
            
            echo "العنوان بعد التحديث: {$updated['title']}\n";
            
            // إعادة العنوان الأصلي
            $stmt = $pdo->prepare("UPDATE tasks SET title = ? WHERE id = ?");
            $stmt->execute([$originalTitle, $taskId]);
            echo "تم إعادة العنوان الأصلي\n";
        } else {
            echo "❌ فشل في تحديث العنوان\n";
        }
    }
    
    echo "\n5. اختبار API مباشرة:\n";
    
    // اختبار تحميل المهام
    $_POST = ['action' => 'load', 'user_id' => 'nurse_6877c487991f4'];
    ob_start();
    include 'tasks-api.php';
    $output = ob_get_clean();
    
    $result = json_decode($output, true);
    if ($result && $result['success']) {
        echo "✅ API تحميل المهام يعمل - عدد المهام: " . $result['count'] . "\n";
        
        if ($result['count'] > 0) {
            $firstTask = $result['tasks'][0];
            echo "أول مهمة: {$firstTask['title']} (ID: {$firstTask['id']})\n";
            
            // اختبار تحديث الحالة عبر API
            $isCompleted = ($firstTask['status'] === 'completed');
            $_POST = [
                'action' => 'toggle_completion',
                'task_id' => $firstTask['id'],
                'completed' => !$isCompleted
            ];
            
            ob_start();
            include 'tasks-api.php';
            $toggleOutput = ob_get_clean();
            
            $toggleResult = json_decode($toggleOutput, true);
            if ($toggleResult && $toggleResult['success']) {
                echo "✅ API تحديث الحالة يعمل\n";
            } else {
                echo "❌ API تحديث الحالة فشل: " . ($toggleResult['message'] ?? 'خطأ غير معروف') . "\n";
            }
        }
    } else {
        echo "❌ API تحميل المهام فشل: " . ($result['message'] ?? 'خطأ غير معروف') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

echo "\n=== انتهى التشخيص ===\n";
?>
