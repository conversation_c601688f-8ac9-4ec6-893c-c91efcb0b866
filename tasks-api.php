<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // الاتصال بقاعدة البيانات مباشرة
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // إنشاء جدول المهام إذا لم يكن موجوداً
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tasks (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            due_date DATE,
            priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
            completed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // قراءة البيانات
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);
    if (!$input) {
        $input = $_POST;
    }
    
    $action = $input['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'Tasks API يعمل بنجاح',
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'load':
            $user_id = $input['user_id'] ?? '';
            if (!$user_id) {
                throw new Exception('معرف المستخدم مطلوب');
            }
            
            $stmt = $pdo->prepare("SELECT * FROM tasks WHERE user_id = ? ORDER BY created_at DESC");
            $stmt->execute([$user_id]);
            $tasks = $stmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'tasks' => $tasks,
                'count' => count($tasks)
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'save':
            $user_id = $input['user_id'] ?? '';
            $task_id = $input['task_id'] ?? 'task_' . time() . '_' . rand(1000, 9999);
            $title = $input['title'] ?? '';
            $description = $input['description'] ?? '';
            $due_date = $input['due_date'] ?? null;
            $priority = $input['priority'] ?? 'medium';
            $center_id = $input['center_id'] ?? '1';
            
            if (!$user_id || !$title) {
                throw new Exception('معرف المستخدم والعنوان مطلوبان');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO tasks (id, user_id, center_id, title, description, due_date, priority) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$task_id, $user_id, $center_id, $title, $description, $due_date, $priority]);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حفظ المهمة بنجاح',
                'task_id' => $task_id
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update':
            $task_id = $input['task_id'] ?? '';
            $title = $input['title'] ?? '';
            $description = $input['description'] ?? '';
            $due_date = $input['due_date'] ?? null;
            $priority = $input['priority'] ?? 'medium';
            
            if (!$task_id || !$title) {
                throw new Exception('معرف المهمة والعنوان مطلوبان');
            }
            
            $stmt = $pdo->prepare("
                UPDATE tasks 
                SET title = ?, description = ?, due_date = ?, priority = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$title, $description, $due_date, $priority, $task_id]);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المهمة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'delete':
            $task_id = $input['task_id'] ?? '';
            
            if (!$task_id) {
                throw new Exception('معرف المهمة مطلوب');
            }
            
            $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
            $stmt->execute([$task_id]);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف المهمة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'toggle_completion':
            $task_id = $input['task_id'] ?? '';
            $completed = $input['completed'] ?? false;
            
            if (!$task_id) {
                throw new Exception('معرف المهمة مطلوب');
            }
            
            $stmt = $pdo->prepare("
                UPDATE tasks 
                SET completed = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$completed ? 1 : 0, $task_id]);
            
            echo json_encode([
                'success' => true,
                'message' => $completed ? 'تم تمييز المهمة كمكتملة' : 'تم تمييز المهمة كغير مكتملة'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'file' => 'tasks-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
