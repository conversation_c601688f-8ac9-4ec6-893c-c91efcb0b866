<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 0); // إخفاء الأخطاء من المتصفح
ini_set('log_errors', 1);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // تضمين إعدادات قاعدة البيانات
    if (!file_exists('config/database-live.php')) {
        throw new Exception('ملف إعدادات قاعدة البيانات غير موجود');
    }

    require_once 'config/database-live.php';

    // الاتصال بقاعدة البيانات
    $pdo = getDatabaseConnection();

    if (!$pdo) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }

    // إنشاء جدول المهام إذا لم يكن موجوداً
    createTasksTable($pdo);

    // قراءة البيانات المرسلة
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        $input = $_POST;
    }

    // إضافة معلومات تشخيصية
    error_log("Tasks API - Method: " . $_SERVER['REQUEST_METHOD'] . ", Input: " . print_r($input, true));

    $action = $input['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('لم يتم تحديد الإجراء المطلوب');
    }

    switch ($action) {
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'Tasks API يعمل بنجاح',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'load':
            loadTasks($pdo, $input);
            break;
            
        case 'save':
            saveTask($pdo, $input);
            break;
            
        case 'update':
            updateTask($pdo, $input);
            break;
            
        case 'delete':
            deleteTask($pdo, $input);
            break;
            
        case 'toggle_completion':
            toggleTaskCompletion($pdo, $input);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    error_log('خطأ في قاعدة البيانات tasks-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_type' => 'database',
        'file' => 'tasks-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    error_log('خطأ في tasks-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في Tasks API: ' . $e->getMessage(),
        'error_type' => 'general',
        'file' => 'tasks-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * إنشاء جدول المهام
 */
function createTasksTable($pdo) {
    $sql = "
    CREATE TABLE IF NOT EXISTS tasks (
        id VARCHAR(50) PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        center_id VARCHAR(50) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        due_date DATE,
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        completed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_center_id (center_id),
        INDEX idx_due_date (due_date),
        INDEX idx_completed (completed)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
}

/**
 * تحميل المهام
 */
function loadTasks($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    $stmt = $pdo->prepare("
        SELECT id, title, description, due_date, priority, completed, created_at, updated_at 
        FROM tasks 
        WHERE user_id = ? 
        ORDER BY created_at DESC
    ");
    $stmt->execute([$user_id]);
    $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'tasks' => $tasks,
        'count' => count($tasks),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * حفظ مهمة جديدة
 */
function saveTask($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '1';
    $task_id = $input['task_id'] ?? '';
    $title = $input['title'] ?? '';
    $description = $input['description'] ?? '';
    $due_date = $input['due_date'] ?? null;
    $priority = $input['priority'] ?? 'medium';
    
    if (!$user_id || !$title) {
        throw new Exception('معرف المستخدم والعنوان مطلوبان');
    }
    
    // إنشاء معرف فريد إذا لم يتم توفيره
    if (!$task_id) {
        $task_id = 'task_' . time() . '_' . rand(1000, 9999);
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO tasks (id, user_id, center_id, title, description, due_date, priority) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([$task_id, $user_id, $center_id, $title, $description, $due_date, $priority]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ المهمة بنجاح',
        'task_id' => $task_id,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * تحديث مهمة
 */
function updateTask($pdo, $input) {
    $task_id = $input['task_id'] ?? '';
    $title = $input['title'] ?? '';
    $description = $input['description'] ?? '';
    $due_date = $input['due_date'] ?? null;
    $priority = $input['priority'] ?? 'medium';
    
    if (!$task_id || !$title) {
        throw new Exception('معرف المهمة والعنوان مطلوبان');
    }
    
    $stmt = $pdo->prepare("
        UPDATE tasks 
        SET title = ?, description = ?, due_date = ?, priority = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");
    
    $stmt->execute([$title, $description, $due_date, $priority, $task_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث المهمة بنجاح',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * حذف مهمة
 */
function deleteTask($pdo, $input) {
    $task_id = $input['task_id'] ?? '';
    
    if (!$task_id) {
        throw new Exception('معرف المهمة مطلوب');
    }
    
    $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
    $stmt->execute([$task_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف المهمة بنجاح',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * تبديل حالة إكمال المهمة
 */
function toggleTaskCompletion($pdo, $input) {
    $task_id = $input['task_id'] ?? '';
    $completed = $input['completed'] ?? false;

    if (!$task_id) {
        throw new Exception('معرف المهمة مطلوب');
    }

    $stmt = $pdo->prepare("
        UPDATE tasks 
        SET completed = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");
    
    $stmt->execute([$completed ? 1 : 0, $task_id]);
    
    echo json_encode([
        'success' => true,
        'message' => $completed ? 'تم تمييز المهمة كمكتملة' : 'تم تمييز المهمة كغير مكتملة',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
