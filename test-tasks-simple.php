<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== اختبار بسيط لـ tasks-api.php ===\n\n";

// 1. اختبار وجود الملف
echo "1. فحص وجود الملف:\n";
if (file_exists('tasks-api.php')) {
    echo "✅ tasks-api.php موجود\n";
    echo "حجم الملف: " . filesize('tasks-api.php') . " بايت\n";
} else {
    echo "❌ tasks-api.php غير موجود\n";
}

echo "\n2. اختبار تضمين الملف:\n";
try {
    ob_start();
    include 'tasks-api.php';
    $output = ob_get_clean();
    echo "✅ تم تضمين الملف بنجاح\n";
    if ($output) {
        echo "المخرجات: " . substr($output, 0, 200) . "...\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في تضمين الملف: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ خطأ PHP: " . $e->getMessage() . "\n";
}

echo "\n3. اختبار مباشر عبر HTTP:\n";
$testUrl = "http://" . $_SERVER['HTTP_HOST'] . "/tasks-api.php";
echo "URL: $testUrl\n";

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode(['action' => 'test']),
        'timeout' => 5
    ]
]);

$response = @file_get_contents($testUrl, false, $context);
if ($response !== false) {
    echo "✅ استجابة HTTP موجودة\n";
    echo "الاستجابة: " . substr($response, 0, 200) . "\n";
    
    $json = json_decode($response, true);
    if ($json) {
        echo "✅ JSON صحيح\n";
        echo "النجاح: " . ($json['success'] ? 'نعم' : 'لا') . "\n";
    } else {
        echo "❌ JSON غير صحيح\n";
    }
} else {
    echo "❌ لا توجد استجابة HTTP\n";
    
    // فحص الأخطاء
    $error = error_get_last();
    if ($error) {
        echo "آخر خطأ: " . $error['message'] . "\n";
    }
}

echo "\n4. فحص إعدادات قاعدة البيانات:\n";
try {
    require_once 'config/database-live.php';
    $pdo = getDatabaseConnection();
    echo "✅ الاتصال بقاعدة البيانات نجح\n";
    
    // فحص جدول المهام
    $stmt = $pdo->query("SHOW TABLES LIKE 'tasks'");
    if ($stmt->rowCount() > 0) {
        echo "✅ جدول المهام موجود\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM tasks");
        $result = $stmt->fetch();
        echo "عدد المهام: " . $result['count'] . "\n";
    } else {
        echo "⚠️ جدول المهام غير موجود\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}

echo "\n=== انتهى الاختبار ===\n";
?>
