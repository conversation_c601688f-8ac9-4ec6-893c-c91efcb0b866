<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== اختبار سريع لـ Tasks API ===\n\n";

// اختبار الاتصال
echo "1. اختبار الاتصال:\n";
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode(['action' => 'test']),
        'timeout' => 10
    ]
]);

$response = @file_get_contents("http://" . $_SERVER['HTTP_HOST'] . "/tasks-api.php", false, $context);
if ($response) {
    $result = json_decode($response, true);
    if ($result && $result['success']) {
        echo "✅ Tasks API يعمل: " . $result['message'] . "\n";
    } else {
        echo "❌ Tasks API فشل: " . ($result['message'] ?? 'خطأ غير معروف') . "\n";
    }
} else {
    echo "❌ Tasks API لا يستجيب\n";
}

echo "\n2. اختبار تحميل المهام:\n";
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode([
            'action' => 'load',
            'user_id' => 'nurse_6877c487991f4'
        ]),
        'timeout' => 10
    ]
]);

$response = @file_get_contents("http://" . $_SERVER['HTTP_HOST'] . "/tasks-api.php", false, $context);
if ($response) {
    $result = json_decode($response, true);
    if ($result && $result['success']) {
        echo "✅ تحميل المهام نجح: " . $result['count'] . " مهمة\n";
    } else {
        echo "❌ تحميل المهام فشل: " . ($result['message'] ?? 'خطأ غير معروف') . "\n";
    }
} else {
    echo "❌ تحميل المهام لا يستجيب\n";
}

echo "\n=== انتهى الاختبار ===\n";
?>
