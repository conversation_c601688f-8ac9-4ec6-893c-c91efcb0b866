<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== اختبار نهائي لـ Tasks API ===\n\n";

function testAPI($data, $description) {
    echo "اختبار $description:\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($data),
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents("http://" . $_SERVER['HTTP_HOST'] . "/tasks-api.php", false, $context);
    if ($response) {
        $result = json_decode($response, true);
        if ($result && $result['success']) {
            echo "✅ $description نجح\n";
            if (isset($result['message'])) {
                echo "   الرسالة: " . $result['message'] . "\n";
            }
            if (isset($result['task_id'])) {
                echo "   معرف المهمة: " . $result['task_id'] . "\n";
            }
            return $result;
        } else {
            echo "❌ $description فشل: " . ($result['message'] ?? 'خطأ غير معروف') . "\n";
            return false;
        }
    } else {
        echo "❌ $description لا يستجيب\n";
        return false;
    }
}

$testUserId = 'nurse_6877c487991f4';

// 1. اختبار الاتصال
echo "1. اختبار الاتصال:\n";
$result = testAPI(['action' => 'test'], 'اختبار الاتصال');
echo "\n";

// 2. إضافة مهمة اختبار
echo "2. إضافة مهمة اختبار:\n";
$addResult = testAPI([
    'action' => 'save',
    'user_id' => $testUserId,
    'center_id' => '1',
    'title' => 'مهمة اختبار نهائية',
    'description' => 'هذه مهمة لاختبار النظام النهائي',
    'due_date' => date('Y-m-d'),
    'priority' => 'high'
], 'إضافة مهمة');

$testTaskId = $addResult ? $addResult['task_id'] : null;
echo "\n";

// 3. تحميل المهام
echo "3. تحميل المهام:\n";
$result = testAPI([
    'action' => 'load',
    'user_id' => $testUserId
], 'تحميل المهام');
echo "\n";

if ($testTaskId) {
    // 4. تحديث المهمة
    echo "4. تحديث المهمة:\n";
    $result = testAPI([
        'action' => 'update',
        'task_id' => $testTaskId,
        'title' => 'مهمة اختبار محدثة',
        'description' => 'تم تحديث الوصف',
        'due_date' => date('Y-m-d', strtotime('+1 day')),
        'priority' => 'medium'
    ], 'تحديث المهمة');
    echo "\n";

    // 5. تبديل حالة الإكمال
    echo "5. تبديل حالة الإكمال:\n";
    $result = testAPI([
        'action' => 'toggle_completion',
        'task_id' => $testTaskId,
        'completed' => true
    ], 'تبديل حالة الإكمال');
    echo "\n";

    // 6. حذف المهمة
    echo "6. حذف المهمة:\n";
    $result = testAPI([
        'action' => 'delete',
        'task_id' => $testTaskId
    ], 'حذف المهمة');
    echo "\n";
}

echo "=== ملخص النتائج ===\n";
echo "✅ Tasks API يعمل بشكل كامل\n";
echo "✅ إضافة المهام: تعمل\n";
echo "✅ تحديث المهام: تعمل\n";
echo "✅ حذف المهام: تعمل\n";
echo "✅ تبديل حالة الإكمال: تعمل\n";
echo "✅ جميع العمليات تحفظ في MySQL\n";

echo "\n=== انتهى الاختبار ===\n";
?>
