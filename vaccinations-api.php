<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // تضمين إعدادات قاعدة البيانات
    require_once 'config/database-live.php';
    
    // الاتصال بقاعدة البيانات
    $pdo = getDatabaseConnection();
    
    // إنشاء جدول التلقيحات إذا لم يكن موجوداً
    createVaccinationTables($pdo);
    
    // قراءة البيانات المرسلة
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        $input = $_POST;
    }

    $action = $input['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('لم يتم تحديد الإجراء المطلوب');
    }

    switch ($action) {
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'Vaccinations API يعمل بنجاح',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'save_vaccination_status':
            $user_id = $input['user_id'] ?? '';
            $child_id = $input['child_id'] ?? '';
            $vaccination_index = $input['vaccination_index'] ?? '';
            $is_completed = $input['is_completed'] ?? false;
            $notes = $input['notes'] ?? '';

            if (empty($user_id) || empty($child_id) || $vaccination_index === '') {
                throw new Exception('البيانات المطلوبة مفقودة: user_id, child_id, vaccination_index');
            }

            if ($is_completed) {
                $stmt = $pdo->prepare("
                    INSERT INTO vaccination_status (user_id, child_id, vaccination_index, is_completed, completed_date, notes) 
                    VALUES (?, ?, ?, 1, NOW(), ?)
                    ON DUPLICATE KEY UPDATE 
                    is_completed = 1,
                    completed_date = NOW(),
                    notes = VALUES(notes),
                    updated_at = CURRENT_TIMESTAMP
                ");
                $stmt->execute([$user_id, $child_id, $vaccination_index, $notes]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO vaccination_status (user_id, child_id, vaccination_index, is_completed, completed_date, notes) 
                    VALUES (?, ?, ?, 0, NULL, ?)
                    ON DUPLICATE KEY UPDATE 
                    is_completed = 0,
                    completed_date = NULL,
                    notes = VALUES(notes),
                    updated_at = CURRENT_TIMESTAMP
                ");
                $stmt->execute([$user_id, $child_id, $vaccination_index, $notes]);
            }

            echo json_encode([
                'success' => true,
                'message' => $is_completed ? 'تم حفظ إكمال التلقيح' : 'تم إلغاء إكمال التلقيح',
                'child_id' => $child_id,
                'vaccination_index' => $vaccination_index,
                'is_completed' => $is_completed,
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'load_vaccination_status':
            $user_id = $input['user_id'] ?? '';
            $child_id = $input['child_id'] ?? '';

            if (empty($user_id) || empty($child_id)) {
                throw new Exception('البيانات المطلوبة مفقودة: user_id, child_id');
            }

            $stmt = $pdo->prepare("
                SELECT vaccination_index, is_completed, completed_date, notes 
                FROM vaccination_status 
                WHERE user_id = ? AND child_id = ? AND is_completed = 1
                ORDER BY vaccination_index
            ");
            $stmt->execute([$user_id, $child_id]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $completedVaccinations = [];
            foreach ($results as $row) {
                $completedVaccinations[$row['vaccination_index']] = true;
            }

            echo json_encode([
                'success' => true,
                'child_id' => $child_id,
                'completed_vaccinations' => $completedVaccinations,
                'count' => count($completedVaccinations),
                'details' => $results,
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_child_vaccination_progress':
            $user_id = $input['user_id'] ?? '';
            $child_id = $input['child_id'] ?? '';
            $total_vaccinations = $input['total_vaccinations'] ?? 12;

            if (empty($user_id) || empty($child_id)) {
                throw new Exception('البيانات المطلوبة مفقودة: user_id, child_id');
            }

            $stmt = $pdo->prepare("
                SELECT COUNT(*) as completed_count
                FROM vaccination_status 
                WHERE user_id = ? AND child_id = ? AND is_completed = 1
            ");
            $stmt->execute([$user_id, $child_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            $completed_count = intval($result['completed_count']);
            $progress_percentage = $total_vaccinations > 0 ? round(($completed_count / $total_vaccinations) * 100) : 0;

            echo json_encode([
                'success' => true,
                'child_id' => $child_id,
                'completed_count' => $completed_count,
                'total_vaccinations' => $total_vaccinations,
                'progress_percentage' => $progress_percentage,
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    error_log('خطأ في قاعدة البيانات vaccinations-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_type' => 'database',
        'file' => 'vaccinations-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    error_log('خطأ في vaccinations-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في Vaccinations API: ' . $e->getMessage(),
        'error_type' => 'general',
        'file' => 'vaccinations-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * إنشاء جداول التلقيحات
 */
function createVaccinationTables($pdo) {
    // التأكد من وجود جدول vaccination_status
    $sql = "
    CREATE TABLE IF NOT EXISTS vaccination_status (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(50) NOT NULL,
        child_id VARCHAR(50) NOT NULL,
        vaccination_index INT NOT NULL,
        is_completed TINYINT(1) DEFAULT 0,
        completed_date TIMESTAMP NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_child_vaccination (child_id, vaccination_index),
        INDEX idx_user_id (user_id),
        INDEX idx_child_id (child_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($sql);
}
?>
