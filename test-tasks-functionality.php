<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== اختبار شامل لوظائف المهام ===\n\n";

function testAPI($url, $data, $description) {
    echo "🔍 اختبار: $description\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($data),
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents("http://" . $_SERVER['HTTP_HOST'] . "/$url", false, $context);
    if ($response) {
        $result = json_decode($response, true);
        if ($result && $result['success']) {
            echo "✅ نجح: " . ($result['message'] ?? 'تم بنجاح') . "\n";
            return $result;
        } else {
            echo "❌ فشل: " . ($result['message'] ?? 'خطأ غير معروف') . "\n";
            if (isset($result['error_type'])) {
                echo "   نوع الخطأ: " . $result['error_type'] . "\n";
            }
            return false;
        }
    } else {
        echo "❌ لا يستجيب - تحقق من وجود الملف\n";
        return false;
    }
}

$testUserId = 'nurse_6877c487991f4';
$testTaskId = 'test_task_' . time();

// 1. اختبار الاتصال الأساسي
echo "1️⃣ اختبار الاتصال الأساسي:\n";
$result = testAPI('tasks-api.php', ['action' => 'test'], 'اتصال API');
echo "\n";

// 2. اختبار تحميل المهام الحالية
echo "2️⃣ اختبار تحميل المهام:\n";
$loadResult = testAPI('tasks-api.php', [
    'action' => 'load',
    'user_id' => $testUserId
], 'تحميل المهام الحالية');

if ($loadResult) {
    echo "   عدد المهام الحالية: " . ($loadResult['count'] ?? 0) . "\n";
}
echo "\n";

// 3. اختبار إضافة مهمة جديدة
echo "3️⃣ اختبار إضافة مهمة جديدة:\n";
$saveResult = testAPI('tasks-api.php', [
    'action' => 'save',
    'user_id' => $testUserId,
    'center_id' => '1',
    'task_id' => $testTaskId,
    'title' => 'مهمة اختبار للحذف والتعديل',
    'description' => 'هذه مهمة لاختبار وظائف الحذف والتعديل',
    'due_date' => date('Y-m-d'),
    'priority' => 'medium'
], 'إضافة مهمة جديدة');

if ($saveResult) {
    echo "   معرف المهمة المضافة: " . ($saveResult['task_id'] ?? $testTaskId) . "\n";
}
echo "\n";

// 4. اختبار تحديث المهمة
echo "4️⃣ اختبار تحديث المهمة:\n";
$updateResult = testAPI('tasks-api.php', [
    'action' => 'update',
    'task_id' => $testTaskId,
    'title' => 'مهمة اختبار محدثة ✏️',
    'description' => 'تم تحديث وصف المهمة بنجاح',
    'due_date' => date('Y-m-d', strtotime('+1 day')),
    'priority' => 'high'
], 'تحديث المهمة');
echo "\n";

// 5. اختبار تبديل حالة الإكمال
echo "5️⃣ اختبار تبديل حالة الإكمال:\n";
$toggleResult = testAPI('tasks-api.php', [
    'action' => 'toggle_completion',
    'task_id' => $testTaskId,
    'completed' => true
], 'تمييز المهمة كمكتملة');
echo "\n";

// 6. التحقق من التحديثات
echo "6️⃣ التحقق من التحديثات:\n";
$verifyResult = testAPI('tasks-api.php', [
    'action' => 'load',
    'user_id' => $testUserId
], 'تحميل المهام بعد التحديث');

if ($verifyResult && isset($verifyResult['tasks'])) {
    $updatedTask = null;
    foreach ($verifyResult['tasks'] as $task) {
        if ($task['id'] === $testTaskId) {
            $updatedTask = $task;
            break;
        }
    }
    
    if ($updatedTask) {
        echo "✅ تم العثور على المهمة المحدثة:\n";
        echo "   العنوان: " . $updatedTask['title'] . "\n";
        echo "   الوصف: " . $updatedTask['description'] . "\n";
        echo "   الأولوية: " . $updatedTask['priority'] . "\n";
        echo "   مكتملة: " . ($updatedTask['completed'] ? 'نعم' : 'لا') . "\n";
    } else {
        echo "❌ لم يتم العثور على المهمة المحدثة\n";
    }
}
echo "\n";

// 7. اختبار حذف المهمة
echo "7️⃣ اختبار حذف المهمة:\n";
$deleteResult = testAPI('tasks-api.php', [
    'action' => 'delete',
    'task_id' => $testTaskId
], 'حذف المهمة');
echo "\n";

// 8. التحقق من الحذف
echo "8️⃣ التحقق من الحذف:\n";
$finalResult = testAPI('tasks-api.php', [
    'action' => 'load',
    'user_id' => $testUserId
], 'تحميل المهام بعد الحذف');

if ($finalResult && isset($finalResult['tasks'])) {
    $deletedTaskFound = false;
    foreach ($finalResult['tasks'] as $task) {
        if ($task['id'] === $testTaskId) {
            $deletedTaskFound = true;
            break;
        }
    }
    
    if (!$deletedTaskFound) {
        echo "✅ تم حذف المهمة بنجاح من قاعدة البيانات\n";
    } else {
        echo "❌ المهمة ما زالت موجودة في قاعدة البيانات\n";
    }
    
    echo "   عدد المهام النهائي: " . count($finalResult['tasks']) . "\n";
}

echo "\n=== ملخص النتائج ===\n";
echo "✅ إذا رأيت هذه الرسالة، فإن جميع APIs تعمل بشكل صحيح\n";
echo "🔧 إذا كانت هناك مشاكل في الواجهة، فالمشكلة في JavaScript\n";
echo "📋 تحقق من console المتصفح للأخطاء في الواجهة\n";

echo "\n=== انتهى الاختبار ===\n";
?>
