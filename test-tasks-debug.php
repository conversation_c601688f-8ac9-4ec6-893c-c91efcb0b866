<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== اختبار تشخيص مشاكل المهام ===\n\n";

try {
    // تضمين إعدادات قاعدة البيانات
    require_once 'config/database-live.php';
    
    echo "1. اختبار الاتصال بقاعدة البيانات:\n";
    $pdo = getDatabaseConnection();
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    echo "2. فحص جدول المهام:\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'tasks'");
    if ($stmt->rowCount() > 0) {
        echo "✅ جدول المهام موجود\n";
        
        // فحص بنية الجدول
        echo "\n3. بنية جدول المهام:\n";
        $stmt = $pdo->query("DESCRIBE tasks");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $column) {
            echo "- {$column['Field']}: {$column['Type']} ({$column['Null']}, {$column['Key']}, {$column['Extra']})\n";
        }
        
        // عدد المهام
        echo "\n4. عدد المهام الموجودة:\n";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM tasks");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "عدد المهام: " . $result['count'] . "\n";
        
        if ($result['count'] > 0) {
            echo "\n5. عينة من المهام:\n";
            $stmt = $pdo->query("SELECT id, title, completed, created_at FROM tasks LIMIT 3");
            $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($tasks as $task) {
                echo "- ID: {$task['id']}, العنوان: {$task['title']}, مكتملة: " . ($task['completed'] ? 'نعم' : 'لا') . "\n";
            }
        }
        
    } else {
        echo "❌ جدول المهام غير موجود\n";
        echo "سيتم إنشاؤه الآن...\n";
        
        $sql = "
        CREATE TABLE tasks (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            due_date DATE,
            priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
            completed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id),
            INDEX idx_due_date (due_date),
            INDEX idx_completed (completed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        echo "✅ تم إنشاء جدول المهام بنجاح\n";
    }
    
    echo "\n6. اختبار إضافة مهمة:\n";
    $testTaskId = 'test_' . time();
    $stmt = $pdo->prepare("
        INSERT INTO tasks (id, user_id, center_id, title, description, priority) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $result = $stmt->execute([
        $testTaskId,
        'nurse_6877c487991f4',
        '1',
        'مهمة اختبار',
        'هذه مهمة للاختبار',
        'medium'
    ]);
    
    if ($result) {
        echo "✅ تم إضافة مهمة اختبار بنجاح (ID: $testTaskId)\n";
        
        echo "\n7. اختبار تحديث المهمة:\n";
        $stmt = $pdo->prepare("
            UPDATE tasks 
            SET title = ?, description = ?, priority = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        $result = $stmt->execute([
            'مهمة اختبار محدثة',
            'تم تحديث هذه المهمة',
            'high',
            $testTaskId
        ]);
        
        if ($result) {
            echo "✅ تم تحديث المهمة بنجاح\n";
        } else {
            echo "❌ فشل في تحديث المهمة\n";
        }
        
        echo "\n8. اختبار حذف المهمة:\n";
        $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
        $result = $stmt->execute([$testTaskId]);
        
        if ($result) {
            echo "✅ تم حذف المهمة بنجاح\n";
        } else {
            echo "❌ فشل في حذف المهمة\n";
        }
        
    } else {
        echo "❌ فشل في إضافة مهمة اختبار\n";
    }
    
    echo "\n9. اختبار tasks-api.php:\n";
    
    // اختبار تحميل المهام
    $testData = json_encode([
        'action' => 'load',
        'user_id' => 'nurse_6877c487991f4'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $testData,
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents("http://" . $_SERVER['HTTP_HOST'] . "/tasks-api.php", false, $context);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "✅ tasks-api.php يعمل بنجاح\n";
            echo "عدد المهام المحملة: " . count($data['tasks']) . "\n";
        } else {
            echo "❌ tasks-api.php فشل: " . ($data['message'] ?? 'خطأ غير معروف') . "\n";
        }
    } else {
        echo "❌ tasks-api.php لا يستجيب\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "\n";
}

echo "\n=== انتهى التشخيص ===\n";
?>
