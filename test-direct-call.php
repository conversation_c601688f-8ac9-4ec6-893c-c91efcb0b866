<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== اختبار مباشر لـ tasks-api.php ===\n\n";

// محاكاة POST request مباشرة
$_POST['action'] = 'test';

echo "1. اختبار مباشر بـ include:\n";
try {
    ob_start();
    include 'tasks-api.php';
    $output = ob_get_clean();
    
    echo "✅ تم تنفيذ الملف\n";
    echo "المخرجات: $output\n";
    
    $json = json_decode($output, true);
    if ($json && isset($json['success'])) {
        echo "✅ JSON صحيح - النجاح: " . ($json['success'] ? 'نعم' : 'لا') . "\n";
        if (isset($json['message'])) {
            echo "الرسالة: " . $json['message'] . "\n";
        }
    } else {
        echo "❌ JSON غير صحيح أو مفقود\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ خطأ PHP: " . $e->getMessage() . "\n";
}

echo "\n2. اختبار تحميل المهام:\n";
$_POST['action'] = 'load';
$_POST['user_id'] = 'nurse_6877c487991f4';

try {
    ob_start();
    include 'tasks-api.php';
    $output = ob_get_clean();
    
    echo "✅ تم تنفيذ تحميل المهام\n";
    
    $json = json_decode($output, true);
    if ($json && isset($json['success']) && $json['success']) {
        echo "✅ تحميل المهام نجح\n";
        echo "عدد المهام: " . ($json['count'] ?? 0) . "\n";
        
        if (isset($json['tasks']) && is_array($json['tasks']) && count($json['tasks']) > 0) {
            echo "أول مهمة: " . $json['tasks'][0]['title'] . "\n";
        }
    } else {
        echo "❌ فشل تحميل المهام: " . ($json['message'] ?? 'خطأ غير معروف') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في تحميل المهام: " . $e->getMessage() . "\n";
}

echo "\n3. اختبار إضافة مهمة:\n";
// لا نرسل task_id ليتم إنشاؤه تلقائياً
$_POST = [
    'action' => 'save',
    'user_id' => 'nurse_6877c487991f4',
    'title' => 'مهمة اختبار مباشر',
    'description' => 'اختبار إضافة مهمة',
    'priority' => 'medium'
];

$testTaskId = null;

try {
    ob_start();
    include 'tasks-api.php';
    $output = ob_get_clean();
    
    $json = json_decode($output, true);
    if ($json && isset($json['success']) && $json['success']) {
        echo "✅ إضافة المهمة نجحت\n";
        $testTaskId = $json['task_id'] ?? null;
        echo "معرف المهمة: " . $testTaskId . "\n";

        // اختبار حذف المهمة
        echo "\n4. اختبار حذف المهمة:\n";
        $_POST = [
            'action' => 'delete',
            'task_id' => $testTaskId
        ];
        
        ob_start();
        include 'tasks-api.php';
        $deleteOutput = ob_get_clean();
        
        $deleteJson = json_decode($deleteOutput, true);
        if ($deleteJson && isset($deleteJson['success']) && $deleteJson['success']) {
            echo "✅ حذف المهمة نجح\n";
        } else {
            echo "❌ فشل حذف المهمة: " . ($deleteJson['message'] ?? 'خطأ غير معروف') . "\n";
        }
        
    } else {
        echo "❌ فشل إضافة المهمة: " . ($json['message'] ?? 'خطأ غير معروف') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إضافة المهمة: " . $e->getMessage() . "\n";
}

echo "\n=== النتيجة ===\n";
echo "إذا رأيت رسائل ✅ أعلاه، فإن tasks-api.php يعمل بشكل صحيح\n";
echo "المشكلة قد تكون في:\n";
echo "1. إعدادات الخادم (mod_rewrite, .htaccess)\n";
echo "2. مشكلة في طلبات HTTP POST\n";
echo "3. مشكلة في JavaScript في الواجهة\n";

echo "\n=== انتهى الاختبار ===\n";
?>
